schemas:
  CreateParkingLotRequest:
    type: object
    required: [name, address, latitude, longitude, totalSpots, hourlyRate]
    properties:
      name:
        type: string
        maxLength: 200
      address:
        type: string
        maxLength: 500
      latitude:
        type: number
        format: double
        minimum: -90
        maximum: 90
      longitude:
        type: number
        format: double
        minimum: -180
        maximum: 180
      totalSpots:
        type: integer
        minimum: 1
      heightLimitCm:
        type: integer
        minimum: 1
      hourlyRate:
        type: integer
        minimum: 1
        description: "Rate in JPY"
      dailyMaxRate:
        type: integer
        minimum: 1
        description: "Maximum daily rate in JPY"
      freeMinutes:
        type: integer
        minimum: 0
        default: 30
      is24h:
        type: boolean
        default: true
      openTime:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
      closeTime:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
      features:
        type: array
        items:
          type: string
      operatorName:
        type: string
        maxLength: 200
      contactPhone:
        type: string
        maxLength: 20

  UpdateParkingLotRequest:
    type: object
    properties:
      name:
        type: string
        maxLength: 200
      address:
        type: string
        maxLength: 500
      latitude:
        type: number
        format: double
        minimum: -90
        maximum: 90
      longitude:
        type: number
        format: double
        minimum: -180
        maximum: 180
      totalSpots:
        type: integer
        minimum: 1
      heightLimitCm:
        type: integer
        minimum: 1
      hourlyRate:
        type: integer
        minimum: 1
        description: "Rate in JPY"
      dailyMaxRate:
        type: integer
        minimum: 1
        description: "Maximum daily rate in JPY"
      freeMinutes:
        type: integer
        minimum: 0
      is24h:
        type: boolean
      openTime:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
      closeTime:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
      features:
        type: array
        items:
          type: string
      status:
        $ref: '#/components/schemas/LotStatus'
      operatorName:
        type: string
        maxLength: 200
      contactPhone:
        type: string
        maxLength: 20

  AdminParkingLotDetails:
    allOf:
      - $ref: '#/components/schemas/ParkingLot'
      - type: object
        properties:
          activeConfig:
            $ref: '#/components/schemas/ParkingLotConfig'
          totalSessions:
            type: integer
            description: "Total number of sessions"
          activeSessions:
            type: integer
            description: "Current active sessions"
          totalRevenue:
            type: integer
            description: "Total revenue in JPY"

  ParkingLotConfig:
    type: object
    properties:
      id:
        type: string
        format: uuid
      parkingLotId:
        type: string
        format: uuid
      configName:
        type: string
      isActive:
        type: boolean
      pricingRules:
        $ref: '#/components/schemas/PricingRules'
      effectiveFrom:
        type: string
        format: date-time
      effectiveUntil:
        type: string
        format: date-time
      createdAt:
        type: string
        format: date-time
      updatedAt:
        type: string
        format: date-time
      createdBy:
        type: string
        format: uuid

  PricingRules:
    type: object
    required: [lot_id, initial_free_minutes, daily_cap, rules]
    properties:
      lot_id:
        type: string
      initial_free_minutes:
        type: integer
        minimum: 0
      daily_cap:
        type: integer
        minimum: 0
      night_caps:
        type: array
        items:
          $ref: '#/components/schemas/NightCap'
      overrides:
        type: array
        items:
          $ref: '#/components/schemas/PriceOverride'
      rules:
        type: array
        items:
          $ref: '#/components/schemas/PricingRule'

  NightCap:
    type: object
    required: [start, end, cap]
    properties:
      start:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: "Start time (HH:MM)"
      end:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: "End time (HH:MM)"
      cap:
        type: integer
        minimum: 0
        description: "Maximum fee for this night window in JPY"

  PriceOverride:
    type: object
    required: [name, start, end, unit_minutes, price_per_unit]
    properties:
      name:
        type: string
        description: "Override name (e.g., 'New Year Holiday')"
      start:
        type: string
        format: date-time
        description: "Start datetime (ISO 8601)"
      end:
        type: string
        format: date-time
        description: "End datetime (ISO 8601)"
      unit_minutes:
        type: integer
        minimum: 1
        description: "Billing unit in minutes"
      price_per_unit:
        type: integer
        minimum: 0
        description: "Price per unit in JPY"

  PricingRule:
    type: object
    required: [days, start, end, unit_minutes, price_per_unit]
    properties:
      days:
        type: array
        items:
          type: string
          enum: [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
        description: "Days of week this rule applies"
      start:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: "Start time (HH:MM)"
      end:
        type: string
        pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: "End time (HH:MM)"
      unit_minutes:
        type: integer
        minimum: 1
        description: "Billing unit in minutes"
      price_per_unit:
        type: integer
        minimum: 0
        description: "Price per unit in JPY"

  CreateParkingLotConfigRequest:
    type: object
    required: [configName, pricingRules]
    properties:
      configName:
        type: string
        maxLength: 200
      pricingRules:
        $ref: '#/components/schemas/PricingRules'
      effectiveFrom:
        type: string
        format: date-time
      effectiveUntil:
        type: string
        format: date-time

  UpdateParkingLotConfigRequest:
    type: object
    properties:
      configName:
        type: string
        maxLength: 200
      pricingRules:
        $ref: '#/components/schemas/PricingRules'
      effectiveFrom:
        type: string
        format: date-time
      effectiveUntil:
        type: string
        format: date-time

  ActivateConfigRequest:
    type: object
    required: [effectiveFrom]
    properties:
      effectiveFrom:
        type: string
        format: date-time
      effectiveUntil:
        type: string
        format: date-time
