# Admin endpoints for parking lot management
/admin/parking-lots:
  get:
    tags: [Admin]
    summary: List all parking lots (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: limit
        in: query
        schema:
          type: integer
          default: 20
          maximum: 100
      - name: offset
        in: query
        schema:
          type: integer
          default: 0
      - name: status
        in: query
        schema:
          $ref: '../parking-api.yaml#/components/schemas/LotStatus'
      - name: search
        in: query
        schema:
          type: string
        description: Search by name or address
    responses:
      '200':
        description: Parking lots retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/ParkingLotsResponse'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

  post:
    tags: [Admin]
    summary: Create a new parking lot (Admin)
    security:
      - BearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateParkingLotRequest'
    responses:
      '201':
        description: Parking lot created successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/ParkingLot'
      '400':
        $ref: '../master.yaml#/components/responses/BadRequest'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

/admin/parking-lots/{lotId}:
  get:
    tags: [Admin]
    summary: Get parking lot details (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Parking lot details retrieved successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminParkingLotDetails'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

  put:
    tags: [Admin]
    summary: Update parking lot (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UpdateParkingLotRequest'
    responses:
      '200':
        description: Parking lot updated successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/ParkingLot'
      '400':
        $ref: '../master.yaml#/components/responses/BadRequest'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

  delete:
    tags: [Admin]
    summary: Delete parking lot (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Parking lot deleted successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/MessageResponse'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

/admin/parking-lots/{lotId}/configs:
  get:
    tags: [Admin]
    summary: Get parking lot pricing configurations (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - name: includeInactive
        in: query
        schema:
          type: boolean
          default: false
    responses:
      '200':
        description: Parking lot configurations retrieved successfully
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ParkingLotConfig'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

  post:
    tags: [Admin]
    summary: Create parking lot pricing configuration (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateParkingLotConfigRequest'
    responses:
      '201':
        description: Parking lot configuration created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParkingLotConfig'
      '400':
        $ref: '../master.yaml#/components/responses/BadRequest'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

/admin/parking-lots/{lotId}/configs/{configId}:
  get:
    tags: [Admin]
    summary: Get specific parking lot configuration (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - name: configId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Parking lot configuration retrieved successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParkingLotConfig'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

  put:
    tags: [Admin]
    summary: Update parking lot configuration (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - name: configId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UpdateParkingLotConfigRequest'
    responses:
      '200':
        description: Parking lot configuration updated successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParkingLotConfig'
      '400':
        $ref: '../master.yaml#/components/responses/BadRequest'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

  delete:
    tags: [Admin]
    summary: Delete parking lot configuration (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - name: configId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      '200':
        description: Parking lot configuration deleted successfully
        content:
          application/json:
            schema:
              $ref: '../parking-api.yaml#/components/schemas/MessageResponse'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

/admin/parking-lots/{lotId}/configs/{configId}/activate:
  post:
    tags: [Admin]
    summary: Activate parking lot configuration (Admin)
    security:
      - BearerAuth: []
    parameters:
      - name: lotId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - name: configId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ActivateConfigRequest'
    responses:
      '200':
        description: Configuration activated successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParkingLotConfig'
      '400':
        $ref: '../master.yaml#/components/responses/BadRequest'
      '404':
        $ref: '../master.yaml#/components/responses/NotFound'
      '401':
        $ref: '../master.yaml#/components/responses/Unauthorized'
      '403':
        $ref: '../master.yaml#/components/responses/Forbidden'

components:
  schemas:
    CreateParkingLotRequest:
      type: object
      required: [name, address, latitude, longitude, totalSpots, hourlyRate]
      properties:
        name:
          type: string
          maxLength: 200
        address:
          type: string
          maxLength: 500
        latitude:
          type: number
          format: double
          minimum: -90
          maximum: 90
        longitude:
          type: number
          format: double
          minimum: -180
          maximum: 180
        totalSpots:
          type: integer
          minimum: 1
        heightLimitCm:
          type: integer
          minimum: 1
        hourlyRate:
          type: integer
          minimum: 1
          description: "Rate in JPY"
        dailyMaxRate:
          type: integer
          minimum: 1
          description: "Maximum daily rate in JPY"
        freeMinutes:
          type: integer
          minimum: 0
          default: 30
        is24h:
          type: boolean
          default: true
        openTime:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        closeTime:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        features:
          type: array
          items:
            type: string
        operatorName:
          type: string
          maxLength: 200
        contactPhone:
          type: string
          maxLength: 20

    UpdateParkingLotRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
        address:
          type: string
          maxLength: 500
        latitude:
          type: number
          format: double
          minimum: -90
          maximum: 90
        longitude:
          type: number
          format: double
          minimum: -180
          maximum: 180
        totalSpots:
          type: integer
          minimum: 1
        heightLimitCm:
          type: integer
          minimum: 1
        hourlyRate:
          type: integer
          minimum: 1
          description: "Rate in JPY"
        dailyMaxRate:
          type: integer
          minimum: 1
          description: "Maximum daily rate in JPY"
        freeMinutes:
          type: integer
          minimum: 0
        is24h:
          type: boolean
        openTime:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        closeTime:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
        features:
          type: array
          items:
            type: string
        status:
          $ref: '../parking-api.yaml#/components/schemas/LotStatus'
        operatorName:
          type: string
          maxLength: 200
        contactPhone:
          type: string
          maxLength: 20

    AdminParkingLotDetails:
      allOf:
        - $ref: '../parking-api.yaml#/components/schemas/ParkingLot'
        - type: object
          properties:
            activeConfig:
              $ref: '#/components/schemas/ParkingLotConfig'
            totalSessions:
              type: integer
              description: "Total number of sessions"
            activeSessions:
              type: integer
              description: "Current active sessions"
            totalRevenue:
              type: integer
              description: "Total revenue in JPY"

    ParkingLotConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
        parkingLotId:
          type: string
          format: uuid
        configName:
          type: string
        isActive:
          type: boolean
        pricingRules:
          $ref: '#/components/schemas/PricingRules'
        effectiveFrom:
          type: string
          format: date-time
        effectiveUntil:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        createdBy:
          type: string
          format: uuid

    PricingRules:
      type: object
      required: [lot_id, initial_free_minutes, daily_cap, rules]
      properties:
        lot_id:
          type: string
        initial_free_minutes:
          type: integer
          minimum: 0
        daily_cap:
          type: integer
          minimum: 0
        night_caps:
          type: array
          items:
            $ref: '#/components/schemas/NightCap'
        overrides:
          type: array
          items:
            $ref: '#/components/schemas/PriceOverride'
        rules:
          type: array
          items:
            $ref: '#/components/schemas/PricingRule'

    NightCap:
      type: object
      required: [start, end, cap]
      properties:
        start:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          description: "Start time (HH:MM)"
        end:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          description: "End time (HH:MM)"
        cap:
          type: integer
          minimum: 0
          description: "Maximum fee for this night window in JPY"

    PriceOverride:
      type: object
      required: [name, start, end, unit_minutes, price_per_unit]
      properties:
        name:
          type: string
          description: "Override name (e.g., 'New Year Holiday')"
        start:
          type: string
          format: date-time
          description: "Start datetime (ISO 8601)"
        end:
          type: string
          format: date-time
          description: "End datetime (ISO 8601)"
        unit_minutes:
          type: integer
          minimum: 1
          description: "Billing unit in minutes"
        price_per_unit:
          type: integer
          minimum: 0
          description: "Price per unit in JPY"

    PricingRule:
      type: object
      required: [days, start, end, unit_minutes, price_per_unit]
      properties:
        days:
          type: array
          items:
            type: string
            enum: [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
          description: "Days of week this rule applies"
        start:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          description: "Start time (HH:MM)"
        end:
          type: string
          pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
          description: "End time (HH:MM)"
        unit_minutes:
          type: integer
          minimum: 1
          description: "Billing unit in minutes"
        price_per_unit:
          type: integer
          minimum: 0
          description: "Price per unit in JPY"

    CreateParkingLotConfigRequest:
      type: object
      required: [configName, pricingRules]
      properties:
        configName:
          type: string
          maxLength: 200
        pricingRules:
          $ref: '#/components/schemas/PricingRules'
        effectiveFrom:
          type: string
          format: date-time
        effectiveUntil:
          type: string
          format: date-time

    UpdateParkingLotConfigRequest:
      type: object
      properties:
        configName:
          type: string
          maxLength: 200
        pricingRules:
          $ref: '#/components/schemas/PricingRules'
        effectiveFrom:
          type: string
          format: date-time
        effectiveUntil:
          type: string
          format: date-time

    ActivateConfigRequest:
      type: object
      required: [effectiveFrom]
      properties:
        effectiveFrom:
          type: string
          format: date-time
        effectiveUntil:
          type: string
          format: date-time
