// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+R9a28cy3XgXym0DSx174yGlGTj3gGMXUqULPqSulw+fH1X4lLF7jMzddlT3a6qJjVX",
	"S2CHMAzvOoaRGPkQBEYQIAiCPIEAQT4ESH7MwP8jqFc/qx9DDkey80nUdPWpqvOq86rT7z0/msYRBSq4",
	"N3zvMeBxRDmo/zzFwSH8NAEu5P/8iAqg6k8cxyHxsSARHXzDIyp/4/4Eplj+9V0GI2/ofWeQgR7op3zw",
	"nLGIHZpJvOvr654XAPcZiSUwbyjnRHbS6573LKKjkPhrXEA643XPexGxcxIEQNc3fTbldc/bpQIYxeER",
	"sEtg6t31rcROjvTsSE9/3fNeReJFlNBgfUt5FQmkp7zueScUJ2ISMfItrHEJhVnlY/OmBLydiEn67vC9",
	"F7MoBiaIliLs+8D5cXSh2UjMYvCGHheM0LHcD7yLCQO+m39KqIAxKGzHeDYFKo5AJLEUDML0tovL+2oC",
	"YgIMmdGIy+GIcMTsKz0L+zyKQsCKvxiMGPBJ/doUmN0Uv00YPMqGZnjseQkH1vbqiRwjcWrmj86/AS2A",
	"24mIDvSWcpqohN9plOjllQQZh34SYgEBijG7IHSMRgCIUPSjg68zdORQ7advvACoAnwBgOwQElF0zgBf",
	"BNEV9XqlJZ1jDgZEdZYRoTiseerCwRgoMCzgEHwgsdnoCCeh8IaCJVDm1B+a8QjTAHGgAWL6TYQTEU2x",
	"ID4Ow5mTIeTwV5EgIyNFrZMdSfiW6SRpQ1C4oXkgrpkSDjsasKHvPohJFLROeMIBSZ76bxyZcen0Uw2h",
	"OpvidCs5ry3DlOl92sZ/tQKeMqCLpVjwlGGtLCviJZ/uYS6euJ8mjAH1ZwWceAXmzQYTehkRH14l03Mt",
	"cJURU+Acj9X64R2WlPKGntkcilkktRQEiCdKX42SAo9kYGJMgm2121HEplguKcAC+oJMwT1ezbAbFF5J",
	"EhI0jNbMcKyeOrZiGPqEhW6tJbBIeJvWMVs/0oPVa4zEYH7WmmzXTTeDo9yzPK9VuOhpFEn1U+UcH1Mf",
	"Qq1OavWFGQRLYd1nIPl6mVeABsfyUecXJlHCwtkhFjULJ11JrpTzXiTaKZaOLLzXlbdCs9TGOdQgO7oj",
	"ZC4wE8shrxuPGs7JeFREAofbDQoniYNlCS/VaaeNNvD2UbodoMlUalk/oiPCpsrySFlY/q0PCfU3jc74",
	"JLrKqd5sVc8mmI7hAHN+FbGg9vTXSlLYcQ4zQA9AsR3hmIvCVT2AV3CVvow2poSSaTJFnyF/ghn2BTD+",
	"wOt5U0L3gI7FxBt+5kJc/gAqr7m4ANc59EyJs8F1LS6WluDbidB9CUUJS3au0jLzgDOlVY80JdD17BNi",
	"zgvmzhS/s4R87FJ6hOExprg0dKsOWfYMa9U5aqBCwbi6kq1NF4KBERxmJ35u/JM25JpZemUE5DZYmsCF",
	"YZcFV28nOQ2QHacRh4wa62CN5KZezWm9AwJ8iYwdwvQfebUGVDC5EnhHhFNvpa83eYROqGfm0DbQzxj4",
	"EQvU/zlwTiJ6ZtCiVacB41pEiyfT6BqhjVHEkFwB0jMo/VY9aXLkrFMsOwnLuRH5FRzLcyydPDDj5Aqm",
	"hCYCeNdVGAZoEzGXIW9FNJMgtyMVIMl+SI1FVA92iqOiUVftuBxTPp9iEv4YWCqptRpNWGe+uBkFAF3m",
	"ICA1Eo1YNEVTPCY+Cgm9aFXLGr5LGxRjKFVVGwVuZglAYBLmUZHBBBvzanJnKs/k+cIFnsZLHD2VqV9E",
	"bByJVgMEJGIL8+hf2vCoR512mHgpfWpfQww4CEQoFyzRAoS4VLAiQrMoYah+lZUFvcQsuMIMcqqtBhmP",
	"96qc97gfkDERqHjSZIIUYyGAyZH/+/Vm//PT94+vv+uSmGdbDkVGxAxd4jABtPEjHGMKHJA+21BILgD9",
	"7jfz3/3rb5WB1nKavnSA30IhyMWhl+ZQRBFDmE1nfQZafZrnG897L3tf9PZ7x72vy5MVd7iY/+li/meL",
	"+Z8v5r9dzP9iMf/LxfyvFvO/Xsz/ZjH/28X87xbzv1/M/2Ex/8fF/J8W839ezP9lMf+3xfzfF/P/WNz8",
	"38XNfHFzs7j52eLm54ub/7e4+f+Lm18ubn61mN8s5j9bzH++mP9iMf/lYv6rxfzXi/kfL+a/Wdz8yeLm",
	"14ubP1rc/OL5yy/2j78+deJ3d6eKgCeGdtoSaKDZEzfNlBMQAPUdh1DKTSgbhbgfMSmfmdBGyXkIGqXS",
	"8FYINUa4N8wIaZYm3c4pHoMJDRRn/FL9gUOkhqCTwz2t/QK7kgYbmTg8g+8b7NhjLIwEkvsQZETcaPq+",
	"G02ZM1icYHOIlLELgRRbOQ3aUKbCgx7aGqIQRgKJCRQWsCHPzAfKNFa2xWZv69R1cArHfMdWb6JoVMDK",
	"Lcx3wT0lsz2pFJR4KRbLIzTduEsL7kVjUq9ocmiu7OKEA6N4ClJYlZZDOAiYPGyd5K3z+ySYBq+xtN0S",
	"3Rt8ub1IVL1laeNcajaXNKLSYVbWecQhcJp4+1r7dzoeihuzryA7otk8KYVju1gr5RByyRBYPjbVMZRE",
	"+BEUAiO5gHMHc3UJP3ipoKY8d5fZ7XKWpCAirDGDOvieeVJZF3TdMaTKGnKCIUkDOgigUH6Wjx9Fl8C4",
	"wLOzK8yoBN/zYkZ8OMMhMOH1JP0lYc8YTAkNCl5stvaDQgCy5KoZzeGMz0vhtAGOnKrfeL3Z3zr970rj",
	"/59Hrzf7j08fDF9v9r93qn6qOygF9sXBJKJuWt5CaEyYafsSkxCfk5CIWfcIa+EtaaRjEs728Tsb7i2q",
	"lH19OCM1CqnsU0OeLSBcYLdNYJ4oRxAEMOMHcsDMn0ibNgmF8gSrBkLFEBgBFgkz54WAKW8QEg8zhtVG",
	"RwxgX/ugjhQgg+y0NZ6qc4sTIOOJ2CNTIp5N3ZHaYvi8pKJbMNhVH0o7Z8n9E/7oycStQkMsiEiCUkCv",
	"Fv9hRMfLjJdntnOJUQx0RXImZRuLiL2qm6tbXD47w21M/iiOBF9RSN6lI2sks6qw9NMQGhYUYi5OTCSp",
	"szaJfD+JMfVTju1A0WbMNG+T11s2oRSrmq2ZuVJ+755PKgtCNBpxqJlGbaz7ntIQVbfKBZ3faa5UuENa",
	"+bYnScdM9AiTMGFwCNhU4NzammtNad8yF3237DIDwWbP6jNwxnprr5fRw5aPHN57djs/co/Qi8ZxmS9T",
	"NQrXbEeaJbfEIetDiurJMxOmtAYooTwZjYhPpPE5SmjAPS1hZwH4IaEmXh9LR/BM/q6D89ZcTYtSTIWF",
	"tEb1Ck6dshOG59i/0JEKhwGybdU7skNRpMfqeNcU00QF1nU2xUR0u5/+TV6SYnw5t9s6cLmMIxzyjj5j",
	"jt9uV2pj6uhuU5vSJA1xti63RmjYTFbTVKoPs6q7nJZhAVLP0MaPCcc9tI+5ACa5qodA+A8frKrWQ+Jq",
	"th9RMalZhhqhI7VTOQxtbPW3HrlzMBra14BZO7CZHFVv0jpc+h3LTHU1j2JCOCJcRcI614RpK+hJFao8",
	"ONETpCJ7HEUjJLHflPbJa8vGPKR1x4vzHRQTn2pQL6ui0AolxrMYz5wq4wMpWVXu+WMckgC3JDuzzNtz",
	"KrVHQ/WqHNu3pCMcgX4BSRdQUlcVk7poOcG8vprQzTMaGppgjnAN2yAOIfiipnh2gvmtJqOz0jQc4SCo",
	"mYRwhehnJujReUOXkjLl7Ug05lHMnVNSeCeOBMSuSph3AjHwo+kUaAAB4gJiJCJb+Am67DjHvjgIqich",
	"B3FmEF59qEl+JpdpH3qWyc8qxywFp0yYwCzf9t3Z5wrSKEDA5U4EvgCTcJb/1aspIM1b6jirRnpjoIEN",
	"VVmLoFQWJS1o9QcDaXDUBIAPbDlbW0VLSSG70nBoY+vR41Li6vFqjpp8wUxxJWlSLa2kQhuL+U01f3b7",
	"cPC2Dqs77ZXG3P+LJAzROC0AaMv937XUpzh5OZO50T2LWa4JKldr59J4iuZPSkCffAwnTB6ZVmyohBNq",
	"QZkC84n6DwMq1B9TEhKBmfuIPMxdc6jNKAVwSXxT21aTNtRDcik+pGOTyntDgmH/QguzQyE1XrSoFGfl",
	"RrtSSIcwJtIwvHtRQhZ1c/C/elRgj0ebm0vl0NJqhGnCBToHhAUKQVpY+frJtvLJnhfbuHgJvPw5E8vC",
	"Ql1QGIyAMQj2MB0naW7MxjO+wV75tsGBfQOF5hXkRwGgjW+wor2V1B4Cqn54Tsch4ZNc/lWDBXedljov",
	"l7sAkZ5dkbnkUzrkg0QCV6qDC9Z4AYO5aX9CyU8TfTCqNKrcVxiNCS2i+HubBbI9Lqa7cf/b7f7/2ux/",
	"ftY//fS7renTdDW9lFNzqVfzpEo/t3BwoEGnWqlUTFy1Uib1IxHNFMhy9VQ+C3HXmh+56PZao7hTkXK7",
	"ONXUiKlFmLIwWytk7tp025+G25L/Lm31LtVNWXWoW+kGqoLTuO0ZlIHl5g6H0VEWzVtZ8HZ5IyqtxXhW",
	"KKjpEIIPCPflgpruDNjyy1zay+HkU8Fmu7naGscFEsFmyxWgq0jYfkPMCd4R0TzpO7LkzYvOxuMBLsQk",
	"8rUEKsr25SUwRoI683Jt11q6VcDaLMR9X4TpEJs2MpXFptdtYZoFNNSq5kW0cv5S5Thau+8Kc5RVbneV",
	"5zpjc6dsY9a//sp5er9Mppj2GeBAua7GZjUnaA2kY2dkyixFvoM2ptE5CaGHAuAXIop7SEjw4sFdJCze",
	"zqorSnfPD9LjNxrlse2WVHPHp1uI0JRFVCGWYoMn3M0AKkAoCauh5tkg4Z15oIEtG6rD8lGC/KWq+oyC",
	"gdmQTTUb6J42LUtPJY3g3lv1ergjdkFUaNFn4ED8kYq02qvtChTSbyCuX3GmLrmIplZXOAHaIUiVJ1ZP",
	"mHgCU2A4/AJmDlvRPkUXMFOmspYUdLTzhbvgMDmXHoKUHidAs6jcMAm5g31TMuIP99Rq7CA0CqMrREYq",
	"1AU1ZWopiRqwVUC/C2NO6utUoQplmrxVfWlnMeJfzCSV4xkCYb5M0N9xX8iN/qJT5eaM1SEsb0QXoVaX",
	"7LKpdTXHCW8ICXSJw+sHg4DwNOyp+gWk6CiELKvYDRwx+GWQnEaoC7M6xaiT67ZxRcLQtsBADPoFD46M",
	"kK8umQYPOrhyK4mUqLYIs+c1ay9iX5cu5xsp1MTrFcyDhE9aQcYJn3SAeK/RlnWGVz58jKMqqaYfSjfh",
	"bIgDPaetMooimhYrqvuKLmLfyhtdv5i3iydkV+Sgxm/raJhK609dP1gGKe3aoU0btIn2bQV1hZLZUe5Y",
	"FEKXpj+Hclxnt1G+Ua5nelYw71aVuuikM7rL+6FBh0WdSWPjYErc+Mvt1OUF8ITHKgErrQOTTjx1rYaD",
	"nzAiZkcSg6anG2AGbDvRhR/n6n8vLGJ+9NWxZzpbKU5TT7N9ToSI8/cBLRQisTMBHKjBGnneT/p2WP/Y",
	"hAWtlxATafle98xfFkwpjnawq0xqnIiJ9IXNoX1FxAS93N9+hjgZU1XgrZPd6vHDN9T2yEJ6QXz4hvbR",
	"25/0tw92+1/A7O0QfR0lDBnw5uGRhfV2qID3j15uP/re93NzGDeUafPKvJbel3o7RCeUvEPpxVO0YTMe",
	"csGEosebm9JLiWjAH7yhb2g6Y76l1PANffv27RsaYIHRD9D+8+OXX+6gT9HB9vFL9Cn6nyfPD79Gn6Kn",
	"X+7If453958fHW/vH7yh2Tp/kF//hgTUQzgmZ9pDemAm8Hp1RDNoclHrvJ11SnnMr45NNLlERKn/Vd7d",
	"+p4P39Bd6odJAArL+iWi4yzbpu+aflWveIjeFn4eIs3X6E2yufnYV++rP0FvtoWlr1W16Siy7eSwbjho",
	"Dh+Jgj5P4jhi4n/waRSJSd8cqw/9aJph70g9Qyaep1jsSL/m9bxEemlqPj4cDILI5w8dsCp957ZVcQWD",
	"CVBOLnOXDzDFY9C93mZcwBRJpwpooCUEB5eY+hAgqwLQlARBCFIeH0r++8530JF99MJclJBc/cknctlf",
	"wEzhPaPZJ59UJQMCKw9cz5qxfyaSGqhkhSpAQ7RuPKIBqZsR6l4FoWMJQykXdJ74F9IZDMcRI2Iy1etR",
	"F0vHCVOWkqoat1D0slFWwCRB7dI4EYhjSoRlN91DzeDJ2H7ZzUgN7FmBQHvReGyWdqSuXicsw9PA9teU",
	"xqYcZtfJ1IViOePuDjf0Se/paSF7Q7fDUDFV2qQTjaJQuvUYcYFpgFlAvtXlUlMsUhRwwpXvaRSi3pNd",
	"2sM3VNXL5oASI4n6Xj4EOWIiKSUSuMWNYNgHuWZFrwDOE7Uty2IFaklsGdd+iLY2NzPmiYGZ2zTqTx3U",
	"kcNfGh0ula2E1Vew+p+8RYQGklOycF7CpZEi15QR+smjz5G2LbRfwUAkTLLt1QSoGYfgna9CIinWNWuY",
	"c0RZphLWM91msn88i2GIyp0m9TWlgy+PjgcHJ8fpzh7INy1ETr41XDhEW/tP5aPswq2IQmBSZIf5o0IX",
	"GBRFTSm0kPhgQmhG+ezvHle0TBQD5VHCfHgYsfHAvMQHcmx2c7Cst/Yz1bJ9sOv1vEtg3FyIf7j5cNNe",
	"ycEx8Ybe44ebDx/ru84TZWEMpCAPtGfdz2cs40jHJfQdHFPw7h1EXEjNUOz35OmYCHDxNApmK2vz6W4q",
	"dV0MwUhvS/2Qa4X7aHNzZYso39x1dBtN85wmQFHsg3Pd857o9bimSdc9yPXvVa9stb9SbXOaTKeYzbyh",
	"aciVtcIyTpzV2RCk5Zl4zKW9WtT33qkEp5ljpDpcLMEcxZYY98Qc7oYfa2aOmuYfTTyic+E6XKTS9ndm",
	"lpzr4A1fn+a5wCqzuDB7J6rr+EorrfdMGOY+SFxoKrBmyha6BDvoqdaWL2m4vczW0E7P0FlKTRlaO8VM",
	"fd090cxVvfeRkU7boAZfTm19N9VrUIB0K2lky2y6kFAXCnahoRl5X0QsVix2IuDW2gioen1YZK3quP28",
	"/ZWs2XyjytXrQhhRuFpGfjnQoJDz6MIH5eq5e+OIujK9j88cy69yfQetqjmEat8226atEwMsY2UVKvPu",
	"kewf2sZyVyC2m1jls3ldJngDh+QMsUSlfLqfDfm6jzE42OKHoLjCVpB490iRSpWKgxh2jPTiGYHLezhn",
	"fwjCFrzqYHsafFoKo9LSjRLRx2HYLnR2W3vqne0w9D6srtsOQxSpoim7HSSATQmt9mNdAcb1rnUbNhyG",
	"2ZzwzodY2OjOcuh/n16ov9Z5BXt1rkiBHfV7ngZH6UV8VYeJdQsab/ja5DhiLCZZuJfnRheVVy9Hjrby",
	"xNMPS+4je3tlpTSWLz1pfyn9kkw9U/AYfHn05cv1WjlBHZezfpo8blJv6mw3SdgK1W/dQ1Wxy08TUL2C",
	"Db9Y1VzPK+vkjTaTOL/ZDxqAqjn9NN2KDf7MGZhRottxaLqENZ6ET+0Yt2YokdoktHsdaVFq83/dc0NN",
	"Yj+a6tqrDG5rr4W7MlGnmlD7iYtqLWj1C18Gk7XneN2RfJ6RwJIzpcrpda/hnM0R716iyq7u/Gv2cFMC",
	"1CLcFql/IOc2iyOrZRhn1tDUTdK8bA7em786HeoWyFP7TqcD/Tw3+uM80LtQ2ZaFr8Y7zcim4CLcTLJe",
	"qwL9L0gS0+C80XG5vbUkNeR5caZ6cZqYmpxB1t230UOptP6+JxVa22J8zaGB6tcbHHTNOlfXfDbr7qGg",
	"96Uiq9en18VQvk7lmsYEKTG1JWqJbJpFD2xHaMsTFrThiWJBcIMN9KpUOdzBEEooAxwsZ7DUWD+6658T",
	"Uq7+OOvEtxa7p9DpuIPxU0Dh0hZQpXTbELRIGAdVB+/z/90NrgdTzC7ULTHTVNOfOBSA/LkA+1UByj5m",
	"F4eavO2avLiA32eXOY8ENFX9iRGWxMR39nwlRgtUTgE3E9tUhfVt+8s6Cc6112xzdvdMs1dTGq/n659j",
	"pexUO94aNzfERSltvaQsBb6cCjWdY5efvOQd3WLyI9NrGAck4Vkj4poJ9TC3Wtra3HQpphr9xnWdoANO",
	"1jA5K7/O/aRaXjsLgCvV6CQUwND5DKW9kd27yj12KMSWLoK3VODZBx6WwZrp0uqEeh+nQreL3Lw5q5B+",
	"raHjEWBYMveZh7z2t/CUVFf1weB9GAnjNXXQC/Yra+0aPTQjP05Fnr9V30iDNZjncXW25ek3wKXGz12J",
	"WWgY/YdE2GKPekcqI/e8ULna2e5igEN1UwThIhIbaaeuM/VNc79mSuVuUN05wdax53W+2VAHi/Wg1Ktw",
	"WZuV40sodyIscr9+YpHQHMlbLcaW/p65K3+Sv42+qgibK2Dmuk6sajCDQHKgjqdVr0DXotnBqwN9qarv",
	"mzvazbGBIi308uzt7nsKEzTcI19zoKAkR21yY8iW9m9YMYu8xDQISyyS+xq7n1FlOX4wlf/Q1409O6sy",
	"c69CU8q7fzLUdaNtoItGUna1oYRRuwNERllf1Up7WIGS2N1WdSk0vy/d8e8Q4S7iu3wVt8spH1feuUti",
	"8sl6y+MLdGAwjVZtth0qmAg7z6+7E3jAQfSDrM9FVy1bIvQRCNssY/0kX2XUteE7ws4AbPePBt+RD46g",
	"Mk2u40j9RfYu7NHFNuwYZ102OPqReNPLmKzLGKsTwkXEZksbq3Hx/SohyxQcXMH5JIourOnUSZb5V+Yl",
	"fV7fwVYqXTf/6ApnzUZr8yQN9R3GljH4RUCDOCKFSrA6kuTKvwbaIrcnc19V53QiUVoKZr4in30F4g+k",
	"Nsz1vY0GiVLVNOvwb/KfLin0V94oftvkwZKcYFiwX+jlvhwrHGgQuW+Jr5sXVu9RFT6M/qEu11Q/ze4I",
	"5tQ1ullVRcujVYtW8TtEDbJl+v0vmQI23OjoACSlJudpVgonS+LSpRA8VwS++vK3Uj/QVSWAf59snNzX",
	"wNptHB1y5G318PVGThFAjjNSMhc5Y4DT7yi0Mci2bVLzUeFMr6qy86VRh91wOmCwVJPehsY/tEr0lFat",
	"/LzGdExpxnoqSurzgaq1adSSJ3LcgR62Fs/I9mhu4/9CudDybB8WXs+fJPqH5ph9GSv3VYCrpvhA5beG",
	"Ei2Y1x96+tAFuNtBYLIFBbq6yFpm/sF70w28Q4QyR/YD00K8U4wqHbtKTbbW8GSR5vcanVyGgiwakRDa",
	"9ZcZd4+ngWqCWXcN26zzHvV/kpsmhzO1e63LEpcqS6r4Wb0qq3byXbM7Vkcbs+2GIOtdYgB6222UqXhI",
	"+W6Gxj8CdukuaDtgUZDoKlk9qNIuCcfE9GR7SKg/uNzyHNVhAo+17eAEwfXjfhdQO3AJYRSbbFQF3HAw",
	"CCMfh5OIi+Fnm59tyvUpSKcpZt67BKjUSA3TAOF8s7xM15ZuQ1WXqODlWs2lEsEzIJo4jiK+ghbMgGRv",
	"GiVVfTVfnVPuOJaW/1kg+QqMelDW1nOtI7X2qq9bF1wl3MfmG6Y2FplDQlpS7VpBpS+v3EcWYk43Yrzy",
	"+k0wkHxSJkBa4l9Dv3LVsHmtWEd6fXr9nwEAAP//4PBFdgyiAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
