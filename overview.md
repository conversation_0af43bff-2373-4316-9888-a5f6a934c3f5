## **Smooth.Inc MOTTO**

there are actually two types of system of smooth.inc which would allow us to successfully send number plates to cloud. I will be writing the full over view of how it will work in a paragraph

This smooth.Inc focuses on creating a easier parking lot compared to normal parking lot that includes parking tickets, which we beleive is unfriendly for upcoming automatic taxi that require electric charging. This is why we want to create a framework that does automatic payment, so in the future we could BtoB.

---

## **Usage scenario**

information that User provided are

```
1. legal name
2. home address
3. sufficient payment information
4. plate number

```

1. user enters the parking lot
- Get plate number from hardwares [detected]
- sends data : what it will exactly send are;
    - plate number (which in japan is format like this )e.g.
        - Kanji ###, Hiragana ###-###
        
        ![image-1.png](attachment:333616f9-c27d-4c18-ad02-79f53fd74dbb:image-1.png)
        
    - parking id (which should be connected to a name in AWS databse)
        - e.g. 124u9325y8132 <-> Tokyo Station West Side Parking lot
- sends a time stamp with some value like 'firsttime'
- the User should be able to check its own line app to see it having [parked] status
- and also getting notifications

---

1. User leaves the parking lot
- if sensor doesn't recognize for too much
    - get plate number from hardwares [detected]
    - sends data:
        - plate number
        - parking id
        - time stamp with 'leavingtime'
- > I want these sent data to be stored in a temporary databse so we don't need put too much server to computer power in AWS
1. within AWS

```
1. everytime either 'firsttime' or 'leavingtime' number plate data: String, is sent from all those data, search User id and update the user status

  1. once gained 'leavingtime' to the user id
  2. calculate the parking lot cost
    -> I am still trying to figure out how we will do this
  3. automatically does payment
  4. sents a email to the user 'parked and paid'

```

This means that if OCR fails when parking, it will still have chance to be able to check its number plate when the car is leaving the spot

## **Locally: Parking Lot**

1: entrance pole model

```
Parking Lot
├- Nvidia Jetson Dev kit
|  └ CCTV
|   └ (1)image data
|     └ [Plate Detection, OCR]
|       └ plate number, parking id, time stamp sent to [AWS]
└- Rasberry pi pico
   └ CCTV
    └ image data sent to (1) real time

```

2: each car model

```
Parking Lot
├- Rasberry pi pico
> pico will be place behind the car stop block with a safe smooth.box created by one of our engineers
|  └ Camera
|  └ Infred Light sensor
|  └ Lights
>when the Infred Light sensor detects a object connects a realtime image between Nvidia Jetson Dev Kit which will do [OCR]
├- Nvidia Jetson Dev kit
> stored in a safe place
|   └ (1)image data
|     └ [Plate Detection, OCR]
|       └ plate number, parking id, time stamp sent to [AWS]

---- depending on the size we will configure the number of hardware and we will hybrid the 1 and 2 system model with needs
--- since the detection will be done within Nvidia we will be only sending plate number, parking id, time stamp
```

---

What Nvidia will send to AWS server

```markdown
ts: time

C1: City value, this value can only have about 200 ish
https://www.mlit.go.jp/jidosha/content/001622186.pdf

3L: distinction number with 3 diget

H1: 1 letter of Hiragana and if it is U.S. army related, it will have E, H, K, M, T, Y instead.
normal vehicle:「さ、す、せ、そ、た、ち、つ、て、と、な、に、ぬ、ね、の、は、ひ、ふ、ほ、ま、み、む、め、も、や、ゆ、ら、り、る、ろ」﻿
commercial vehicle:「あ、い、う、え、か、き、く、け、こ、を」﻿
rented cars:「わ、れ」﻿
army related vehicles:「よ」または「E, H, K, M, T, Y」﻿

ID: distinction number with 4 digit
parkingid: distinction number of its parking lot with 6 digit
status status update for 0: started to park, 1: left the parking lot
```

---

## **AWS**

### AWS CPU

- Save information ‘first detected’ ‘last detected’ →Depending on parking id, the final payment should differ
    - calculate parking cost argorithm depended on parking id
    - parking id’s cost information (saved in AWS database)
- Plate Number ↔ User id(saved in AWS database)
- Then automatically does the payment

---

### AWS Website

- Registration of Users
    - plate number
    - user name (original, no copies), pswd
    - payment information(aws payment)
        
        → all saved in AWS databse so that cost calculation and automatic payment to Credit card is possible
        
        - VISA
        - PAYPAY is the minimum payment way that I want it to be added
- Being able to check your account often
    - save multiple plate number
    - make sure there is no duplication of plate number within other accounts
- Have ability to change password when forgotten
    
    → forgot password
    
    → send email
    
    →reset password
    
    → saved to Username
    
    ***MORE features of AWS website (the numbers are assigned to show the priorities, higher the higher the priority is) there might be duplication so you are free to edit this***
    
    ### 1. Search [10]
    
    - **Area selection / current-location detection** – Manually enter an address or landmark, or auto-detect with GPS.
    - **Sort options** – Toggle between *price* order and *distance* order.
    - **Parking-lot data fetch** – Retrieve every lot’s price, distance and available-spot count inside the chosen area.
    - **Filter conditions** – Basic filters such as opening hours, height limits, price range, real-time vacancy, etc.
    
    - **Detailed preview** – One-tap popup showing availability, last-updated time, photos.
    
    ---
    
    ### 2. Booking [1] (this will be the future feature)
    
    - **Date & time picker UI** – Intuitive input in 15-minute blocks or full-day units.
    - **Instant one-tap booking** – Confirm by tapping the ○/× availability indicator.
    - **Save reservation info** – Write to the *reservation* table and verify on the backend.
    - **Booking-complete screen** – Clearly displays reservation ID, lot name, date/time and fee.
    - **Reservation list & cancel** – List upcoming bookings and allow one-tap cancellation.
    
    ---
    
    ### 3. Payment & Billing [10]
    
    - **Download receipt as PDF** – Save/share a PDF receipt after use.
    - **Coupon support** – Apply discount codes or campaign promos.　(for future development we could have this feature stored)
    - **Corporate invoice output** – Auto-generate a monthly consolidated invoice for business users. (this is not in a hurry so [5]
    - **History filter by payment method** – View history [9]
    
    ---
    
    ### 4. Notifications & Alerts [10]
    
    - **Price-change alert** – Notify when a price drop or discount occurs.
    - **Long-stay parking warning** – Alert if parking duration becomes excessive.
    - status change - notify when one of the user’s car is in parked status
    
    ---
    

---

### AWS databse

- User information
    - Username, User password
    - User email
    - Plate Number (this will be multiple)
    - past parking information
        - Tokyo station west parking lot
            
            parked from 1:00am - 4:00 am at 2025/6/1
            
            cost; 3000 yen, payed by ### ### ### card
            
- parking id (Tokyo Station west side parking lot: I want this to be non string but int, numbers)
    - payment algorithm
    
    [] monday: ‘0-8am: 200 yen per h’ 
    
    [] not causing payment when the time is 30 min 
    
    [] max cost should be 2000 yen
    
    →This system should be something that we admins could configure when necessary
    
    - Coordinate
    - A link (for future google map implements)
    - Parking lot name

---

### **PARKING LOT algorithm**

array

```json
{
  "lot_id": "PARK001",

  /global settings
  "initial_free_minutes": 15,       // first 15 min are free
  "daily_cap": 1500,                // max per calendar day (JPY)

  /recurring per-night caps
     Each object defines a window that STARTS every day at <start>
     and ends the next day at <end>.  Any fee incurred within one
     such window is clamped to <cap>.                                */
  "night_caps": [
    {
      "start": "22:00",
      "end":   "08:00",
      "cap":   800                // ¥800 maximum per night window
    }
  ],

  /absolute date-range overrides 
     If a timestamp falls inside ANY override window,
     that override wins and normal weekday rules are ignored.        */
  "overrides": [
    {
      "name": "New-Year Holiday",
      "start": "2025-01-01T00:00",
      "end":   "2025-01-03T23:59",
      "unit_minutes": 60,
      "price_per_unit": 0         // entirely free
    },
    {
      "name": "Concert Day Flat-Rate",
      "start": "2025-05-25T15:00",
      "end":   "2025-05-25T23:00",
      "unit_minutes": 1,          // any non-zero value
      "price_per_unit": 2000      // flat ¥2 000 once
    }
  ],

  / weekday , time-band rules
  "rules": [
    {
      "days": ["Mon","Tue","Wed","Thu","Fri"],
      "start": "15:00",
      "end":   "17:00",
      "unit_minutes": 10,
      "price_per_unit": 100
    },
    {
      "days": ["Mon","Tue","Wed","Thu","Fri"],
      "start": "00:00",
      "end":   "15:00",
      "unit_minutes": 30,
      "price_per_unit": 200
    },
    {
      "days": ["Sat","Sun"],
      "start": "00:00",
      "end":   "24:00",
      "unit_minutes": 60,
      "price_per_unit": 300
    }
  ]
}

```

```python
"""
parking_fee.py  –  stand-alone pricing engine
Compatible with Python 3.10+  (uses | type hints & match)
"""

from __future__ import annotations
import math, json, requests
from datetime import datetime, date, time, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Any

# ─────────────────── week helpers
WEEKDAYS = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

def _str2time(hhmm: str) -> time:
    """'24:00' is allowed and mapped to 00:00 next day."""
    hh, mm = map(int, hhmm.split(":"))
    hh = 0 if hh == 24 else hh
    return time(hh, mm)

# ─────────────────── override helpers
def _override_matches(ovr: dict, ts: datetime) -> bool:
    return datetime.fromisoformat(ovr["start"]) <= ts <= datetime.fromisoformat(
        ovr["end"]
    )

def _override_window_end(ovr: dict) -> datetime:
    return datetime.fromisoformat(ovr["end"])

# ─────────────────── weekday-rule helpers
def _rule_matches(rule: dict, ts: datetime) -> bool:
    if WEEKDAYS[ts.weekday()] not in rule["days"]:
        return False
    start_dt = datetime.combine(ts.date(), _str2time(rule["start"]))
    end_dt = datetime.combine(ts.date(), _str2time(rule["end"]))
    if rule["end"] == "24:00":
        end_dt += timedelta(days=1)
    return start_dt <= ts < end_dt

def _rule_window_end(rule: dict, ts: datetime) -> datetime:
    end_t = _str2time(rule["end"])
    end_dt = datetime.combine(ts.date(), end_t)
    if rule["end"] == "24:00":
        end_dt += timedelta(days=1)
    if end_dt <= ts:
        end_dt += timedelta(days=1)
    return end_dt

# ─────────────────── night-cap helpers
def _night_cap_for(cfg: dict, ts: datetime) -> dict | None:
    """Return the night-cap dict active at timestamp ts (else None)."""
    for cap in cfg.get("night_caps", []):
        start_t = _str2time(cap["start"])
        end_t = _str2time(cap["end"])

        start_dt = datetime.combine(ts.date(), start_t)
        end_dt = datetime.combine(ts.date(), end_t)
        if end_t <= start_t:  # crosses midnight
            end_dt += timedelta(days=1)

        # Allow window that started previous day (e.g. 23:00 the day before)
        if start_dt > ts:
            start_dt -= timedelta(days=1)
            end_dt -= timedelta(days=1)

        if start_dt <= ts < end_dt:
            return cap
    return None

def _night_window_key(ts: datetime, start: time) -> date:
    """
    Returns the 'date key' that identifies one night window.
    Example: start=22:00  ─► window spanning 2025-05-22 22:00 → 2025-05-23 08:00
    returns 2025-05-22.
    """
    if ts.time() >= start:
        return ts.date()
    else:
        return ts.date() - timedelta(days=1)

# ─────────────────── holiday injector
_HOLIDAY_API = "https://date.nager.at/api/v3/PublicHolidays/{year}/JP"

def inject_holidays(cfg: dict, year: int) -> None:
    """
    Fetch JP public holidays for <year> and push free-parking overrides into cfg.
    Only inject if that date is NOT already in overrides.
    """
    res = requests.get(_HOLIDAY_API.format(year=year), timeout=10)
    res.raise_for_status()
    existing = {(o["start"][:10]) for o in cfg.get("overrides", [])}

    for h in res.json():
        if h["date"] in existing:
            continue
        cfg["overrides"].append(
            {
                "name": h["localName"],
                "start": f"{h['date']}T00:00",
                "end": f"{h['date']}T23:59",
                "unit_minutes": 60,
                "price_per_unit": 0,
            }
        )

# ─────────────────── main pricing routine
def calc_parking_fee(entry: datetime, exit: datetime, cfg: dict) -> int:
    if exit <= entry:
        return 0

    free_left = cfg.get("initial_free_minutes", 0)
    total_fee = 0
    now = entry

    # key = date of night-window start  /  value = accumulated cost so far in that window
    nightly_totals: Dict[date, int] = {}

    while now < exit:
        # 1️⃣ absolute override?
        override = next(
            (o for o in cfg.get("overrides", []) if _override_matches(o, now)), None
        )

        # 2️⃣ weekday rule?
        rule = (
            None
            if override
            else next(r for r in cfg["rules"] if _rule_matches(r, now))
        )

        tariff = override or rule
        if not tariff:
            raise ValueError("No pricing rule found for timestamp: %s" % now)

        # determine slice end
        slice_end = (
            min(_override_window_end(tariff), exit)
            if override
            else min(_rule_window_end(tariff, now), exit)
        )

        # minutes in this slice
        minutes = (slice_end - now).total_seconds() / 60

        # apply grace
        if free_left:
            grace = min(free_left, minutes)
            minutes -= grace
            free_left -= grace

        slice_cost = 0
        if minutes > 0:
            units = math.ceil(minutes / tariff["unit_minutes"])
            slice_cost = units * tariff["price_per_unit"]

        # apply night-cap if in effect
        night_cap = _night_cap_for(cfg, now)
        if night_cap:
            night_key = _night_window_key(now, _str2time(night_cap["start"]))
            running = nightly_totals.setdefault(night_key, 0)
            allowed = night_cap["cap"] - running
            if allowed <= 0:
                slice_cost = 0
            elif slice_cost > allowed:
                slice_cost = allowed
            nightly_totals[night_key] += slice_cost

        total_fee += slice_cost
        now = slice_end

    # daily cap (per calendar day, simple clamp)
    if "daily_cap" in cfg:
        total_fee = min(total_fee, cfg["daily_cap"])

    return int(total_fee)

# ─────────────────── quick CLI demo
if __name__ == "__main__":
    cfg = json.loads(Path("parking_cfg.json").read_text(encoding="utf-8"))

    # optional: pull JP holidays once a day / at cold-start
    inject_holidays(cfg, date.today().year)

    jst = timezone(timedelta(hours=9))
    entry_dt = datetime(2025, 5, 22, 21, 30, tzinfo=jst)
    exit_dt = datetime(2025, 5, 23, 8, 45, tzinfo=jst)

    fee = calc_parking_fee(entry_dt, exit_dt, cfg)
    print("Fee:", fee, "JPY")

```

AWS LAMBA?

```python
# handler.py  –  Lambda entry point
import json, os
from datetime import datetime
from parking_fee import calc_parking_fee, inject_holidays, json, Path, date

# Load & hydrate config once at cold-start
CFG_PATH = os.getenv("PARKING_CFG", "parking_cfg.json")
parking_cfg = json.loads(Path(CFG_PATH).read_text(encoding="utf-8"))
inject_holidays(parking_cfg, date.today().year)  # free JP public holidays

def lambda_handler(event, context):
    """
    Expects HTTP POST with JSON:
      {
        "entry": "2025-05-22T14:20:00+09:00",
        "exit":  "2025-05-22T17:05:00+09:00"
      }
    Responds 200 { "fee": 1100 }
    """
    try:
        body = json.loads(event.get("body", "{}"))
        entry = datetime.fromisoformat(body["entry"])
        exit_ = datetime.fromisoformat(body["exit"])

        fee = calc_parking_fee(entry, exit_, parking_cfg)
        return {
            "statusCode": 200,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({"fee": fee}),
        }

    except Exception as exc:  # bad input etc.
        return {
            "statusCode": 400,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({"error": str(exc)}),
        }
```

JETSON

```python
# jetson_client.py
import os, requests, json

API = os.environ["PARKING_API"]  # e.g. https://abc123.execute-api.ap-northeast-1.amazonaws.com

payload = {
    "entry": "2025-05-22T14:20:00+09:00",
    "exit":  "2025-05-22T17:05:00+09:00"
}

resp = requests.post(f"{API}/fee", json=payload, timeout=5)
resp.raise_for_status()
print("Parking fee:", resp.json()["fee"], "JPY")
```

---

### ADMIN access (from smooth.inc)

```markdown
parking_id
parking payment algorithm/information
parking lot name, location via coordinate (so that we can show it on some map on the AWS website)

User information
including User payment issues (both addition and subtraction)
user license plate
```