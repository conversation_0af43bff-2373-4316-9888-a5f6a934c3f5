package parking_fee

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/smooth-inc/backend/internal/domain"
)

var weekdays = []string{"<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"}

type Calculator struct{}

func NewCalculator() *Calculator {
	return &Calculator{}
}

func (c *Calculator) CalculateParkingFee(entry, exit time.Time, config domain.PricingRules) (int, error) {
	if exit.Before(entry) || exit.Equal(entry) {
		return 0, nil
	}

	freeLeft := config.InitialFreeMinutes
	totalFee := 0
	now := entry

	nightlyTotals := make(map[string]int)

	for now.Before(exit) {
		override := c.findMatchingOverride(config.Overrides, now)
		
		var rule *domain.PricingRule
		if override == nil {
			rule = c.findMatchingRule(config.Rules, now)
			if rule == nil {
				return 0, fmt.Errorf("no pricing rule found for timestamp: %s", now.Format(time.RFC3339))
			}
		}

		var tariff interface{}
		if override != nil {
			tariff = override
		} else {
			tariff = rule
		}

		sliceEnd := c.calculateSliceEnd(tariff, now, exit)
		minutes := sliceEnd.Sub(now).Minutes()

		if freeLeft > 0 {
			grace := math.Min(float64(freeLeft), minutes)
			minutes -= grace
			freeLeft -= int(grace)
		}

		sliceCost := 0
		if minutes > 0 {
			var unitMinutes, pricePerUnit int
			if override != nil {
				unitMinutes = override.UnitMinutes
				pricePerUnit = override.PricePerUnit
			} else {
				unitMinutes = rule.UnitMinutes
				pricePerUnit = rule.PricePerUnit
			}
			units := int(math.Ceil(minutes / float64(unitMinutes)))
			sliceCost = units * pricePerUnit
		}

		nightCap := c.findActiveNightCap(config.NightCaps, now)
		if nightCap != nil {
			nightKey := c.getNightWindowKey(now, nightCap.Start)
			running := nightlyTotals[nightKey]
			allowed := nightCap.Cap - running
			if allowed <= 0 {
				sliceCost = 0
			} else if sliceCost > allowed {
				sliceCost = allowed
			}
			nightlyTotals[nightKey] += sliceCost
		}

		totalFee += sliceCost
		now = sliceEnd
	}

	if config.DailyCap > 0 && totalFee > config.DailyCap {
		totalFee = config.DailyCap
	}

	return totalFee, nil
}

func (c *Calculator) findMatchingOverride(overrides []domain.PriceOverride, ts time.Time) *domain.PriceOverride {
	for _, override := range overrides {
		start, err := time.Parse("2006-01-02T15:04", override.Start)
		if err != nil {
			continue
		}
		end, err := time.Parse("2006-01-02T15:04", override.End)
		if err != nil {
			continue
		}

		if (ts.Equal(start) || ts.After(start)) && (ts.Before(end) || ts.Equal(end)) {
			return &override
		}
	}
	return nil
}

func (c *Calculator) findMatchingRule(rules []domain.PricingRule, ts time.Time) *domain.PricingRule {
	weekday := weekdays[int(ts.Weekday()+6)%7] // Convert Go weekday to our format
	
	for _, rule := range rules {
		if !c.containsDay(rule.Days, weekday) {
			continue
		}
		
		startTime, err := c.parseTime(rule.Start)
		if err != nil {
			continue
		}
		endTime, err := c.parseTime(rule.End)
		if err != nil {
			continue
		}
		
		startDT := time.Date(ts.Year(), ts.Month(), ts.Day(), startTime.Hour(), startTime.Minute(), 0, 0, ts.Location())
		endDT := time.Date(ts.Year(), ts.Month(), ts.Day(), endTime.Hour(), endTime.Minute(), 0, 0, ts.Location())
		
		if rule.End == "24:00" {
			endDT = endDT.Add(24 * time.Hour)
		}
		
		if (ts.Equal(startDT) || ts.After(startDT)) && ts.Before(endDT) {
			return &rule
		}
	}
	return nil
}

func (c *Calculator) calculateSliceEnd(tariff interface{}, now, exit time.Time) time.Time {
	switch t := tariff.(type) {
	case *domain.PriceOverride:
		end, err := time.Parse("2006-01-02T15:04", t.End)
		if err != nil {
			return exit
		}
		if end.Before(exit) {
			return end
		}
		return exit
	case *domain.PricingRule:
		endTime, err := c.parseTime(t.End)
		if err != nil {
			return exit
		}
		
		endDT := time.Date(now.Year(), now.Month(), now.Day(), endTime.Hour(), endTime.Minute(), 0, 0, now.Location())
		if t.End == "24:00" {
			endDT = endDT.Add(24 * time.Hour)
		}
		
		if endDT.Before(now) || endDT.Equal(now) {
			endDT = endDT.Add(24 * time.Hour)
		}
		
		if endDT.Before(exit) {
			return endDT
		}
		return exit
	}
	return exit
}

func (c *Calculator) findActiveNightCap(nightCaps []domain.NightCap, ts time.Time) *domain.NightCap {
	for _, cap := range nightCaps {
		startTime, err := c.parseTime(cap.Start)
		if err != nil {
			continue
		}
		endTime, err := c.parseTime(cap.End)
		if err != nil {
			continue
		}
		
		startDT := time.Date(ts.Year(), ts.Month(), ts.Day(), startTime.Hour(), startTime.Minute(), 0, 0, ts.Location())
		endDT := time.Date(ts.Year(), ts.Month(), ts.Day(), endTime.Hour(), endTime.Minute(), 0, 0, ts.Location())
		
		if endTime.Before(startTime) {
			endDT = endDT.Add(24 * time.Hour)
		}
		
		if startDT.After(ts) {
			startDT = startDT.Add(-24 * time.Hour)
			endDT = endDT.Add(-24 * time.Hour)
		}
		
		if (ts.Equal(startDT) || ts.After(startDT)) && ts.Before(endDT) {
			return &cap
		}
	}
	return nil
}

func (c *Calculator) getNightWindowKey(ts time.Time, startTimeStr string) string {
	startTime, err := c.parseTime(startTimeStr)
	if err != nil {
		return ts.Format("2006-01-02")
	}
	
	if ts.Hour() >= startTime.Hour() || (ts.Hour() == startTime.Hour() && ts.Minute() >= startTime.Minute()) {
		return ts.Format("2006-01-02")
	}
	return ts.Add(-24 * time.Hour).Format("2006-01-02")
}

func (c *Calculator) parseTime(timeStr string) (time.Time, error) {
	if timeStr == "24:00" {
		return time.Date(0, 1, 1, 0, 0, 0, 0, time.UTC), nil
	}
	
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return time.Time{}, fmt.Errorf("invalid time format: %s", timeStr)
	}
	
	hour, err := strconv.Atoi(parts[0])
	if err != nil {
		return time.Time{}, err
	}
	minute, err := strconv.Atoi(parts[1])
	if err != nil {
		return time.Time{}, err
	}
	
	return time.Date(0, 1, 1, hour, minute, 0, 0, time.UTC), nil
}

func (c *Calculator) containsDay(days []string, day string) bool {
	for _, d := range days {
		if d == day {
			return true
		}
	}
	return false
}
