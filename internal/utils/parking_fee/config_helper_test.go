package parking_fee

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestCreateDefaultConfig(t *testing.T) {
	lotID := "PARK001"
	parkingLotID := uuid.New()
	createdBy := uuid.New()

	config, err := CreateDefaultConfig(lotID, parkingLotID, createdBy)
	if err != nil {
		t.Fatalf("CreateDefaultConfig failed: %v", err)
	}

	if config.ParkingLotID != parkingLotID {
		t.Errorf("Expected parking lot ID %v, got %v", parkingLotID, config.ParkingLotID)
	}

	if config.PricingRules.LotID != lotID {
		t.Errorf("Expected lot ID %s, got %s", lotID, config.PricingRules.LotID)
	}

	if config.PricingRules.InitialFreeMinutes != 30 {
		t.Errorf("Expected 30 free minutes, got %d", config.PricingRules.InitialFreeMinutes)
	}

	if len(config.PricingRules.Rules) == 0 {
		t.Error("Expected pricing rules to be created")
	}

	if len(config.PricingRules.NightCaps) == 0 {
		t.<PERSON>rror("Expected night caps to be created")
	}
}

func TestCreateBusinessDistrictConfig(t *testing.T) {
	lotID := "PARK002"
	parkingLotID := uuid.New()
	createdBy := uuid.New()

	config, err := CreateBusinessDistrictConfig(lotID, parkingLotID, createdBy)
	if err != nil {
		t.Fatalf("CreateBusinessDistrictConfig failed: %v", err)
	}

	if config.PricingRules.InitialFreeMinutes != 15 {
		t.Errorf("Expected 15 free minutes for business district, got %d", config.PricingRules.InitialFreeMinutes)
	}

	if config.PricingRules.DailyCap != 3000 {
		t.Errorf("Expected daily cap of 3000 for business district, got %d", config.PricingRules.DailyCap)
	}
}

func TestCreateResidentialConfig(t *testing.T) {
	lotID := "PARK003"
	parkingLotID := uuid.New()
	createdBy := uuid.New()

	config, err := CreateResidentialConfig(lotID, parkingLotID, createdBy)
	if err != nil {
		t.Fatalf("CreateResidentialConfig failed: %v", err)
	}

	if config.PricingRules.InitialFreeMinutes != 60 {
		t.Errorf("Expected 60 free minutes for residential, got %d", config.PricingRules.InitialFreeMinutes)
	}

	if config.PricingRules.DailyCap != 1500 {
		t.Errorf("Expected daily cap of 1500 for residential, got %d", config.PricingRules.DailyCap)
	}
}

func TestAddHolidayOverride(t *testing.T) {
	config, _ := CreateDefaultConfig("PARK001", uuid.New(), uuid.New())
	
	initialOverrides := len(config.PricingRules.Overrides)
	
	err := AddHolidayOverride(config, "New Year", "2025-01-01", "2025-01-01", true)
	if err != nil {
		t.Fatalf("AddHolidayOverride failed: %v", err)
	}

	if len(config.PricingRules.Overrides) != initialOverrides+1 {
		t.Errorf("Expected %d overrides, got %d", initialOverrides+1, len(config.PricingRules.Overrides))
	}

	override := config.PricingRules.Overrides[len(config.PricingRules.Overrides)-1]
	if override.Name != "Holiday: New Year" {
		t.Errorf("Expected override name 'Holiday: New Year', got '%s'", override.Name)
	}

	if override.PricePerUnit != 0 {
		t.Errorf("Expected free parking (0 price), got %d", override.PricePerUnit)
	}
}

func TestAddSpecialEventOverride(t *testing.T) {
	config, _ := CreateDefaultConfig("PARK001", uuid.New(), uuid.New())
	
	initialOverrides := len(config.PricingRules.Overrides)
	
	err := AddSpecialEventOverride(config, "Concert Event", "2025-06-15T18:00", "2025-06-15T23:00", 1000)
	if err != nil {
		t.Fatalf("AddSpecialEventOverride failed: %v", err)
	}

	if len(config.PricingRules.Overrides) != initialOverrides+1 {
		t.Errorf("Expected %d overrides, got %d", initialOverrides+1, len(config.PricingRules.Overrides))
	}

	override := config.PricingRules.Overrides[len(config.PricingRules.Overrides)-1]
	if override.Name != "Concert Event" {
		t.Errorf("Expected override name 'Concert Event', got '%s'", override.Name)
	}

	if override.PricePerUnit != 1000 {
		t.Errorf("Expected flat rate of 1000, got %d", override.PricePerUnit)
	}
}

func TestRemoveExpiredHolidays(t *testing.T) {
	config, _ := CreateDefaultConfig("PARK001", uuid.New(), uuid.New())
	
	// Add some holiday overrides
	AddHolidayOverride(config, "Old Holiday", "2023-01-01", "2023-01-01", true)
	AddHolidayOverride(config, "Future Holiday", "2025-12-25", "2025-12-25", true)
	AddSpecialEventOverride(config, "Regular Event", "2024-06-15T18:00", "2024-06-15T23:00", 500)
	
	initialOverrides := len(config.PricingRules.Overrides)
	
	// Remove holidays older than 2024-01-01
	cutoffDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
	RemoveExpiredHolidays(config, cutoffDate)
	
	// Should have removed the old holiday but kept the future holiday and regular event
	expectedOverrides := initialOverrides - 1
	if len(config.PricingRules.Overrides) != expectedOverrides {
		t.Errorf("Expected %d overrides after cleanup, got %d", expectedOverrides, len(config.PricingRules.Overrides))
	}
	
	// Check that the future holiday is still there
	foundFutureHoliday := false
	foundRegularEvent := false
	for _, override := range config.PricingRules.Overrides {
		if override.Name == "Holiday: Future Holiday" {
			foundFutureHoliday = true
		}
		if override.Name == "Regular Event" {
			foundRegularEvent = true
		}
	}
	
	if !foundFutureHoliday {
		t.Error("Future holiday should not have been removed")
	}
	
	if !foundRegularEvent {
		t.Error("Regular event should not have been removed")
	}
}

// Note: TestInjectHolidays is commented out because it requires internet access
// Uncomment and run manually when you want to test the actual API integration
/*
func TestInjectHolidays(t *testing.T) {
	config, _ := CreateDefaultConfig("PARK001", uuid.New(), uuid.New())
	
	initialOverrides := len(config.PricingRules.Overrides)
	
	err := InjectHolidays(config, 2025)
	if err != nil {
		t.Fatalf("InjectHolidays failed: %v", err)
	}
	
	if len(config.PricingRules.Overrides) <= initialOverrides {
		t.Error("Expected holidays to be added")
	}
	
	// Check that at least one holiday was added with the correct format
	foundHoliday := false
	for _, override := range config.PricingRules.Overrides {
		if len(override.Name) > 8 && override.Name[:8] == "Holiday:" {
			foundHoliday = true
			if override.PricePerUnit != 0 {
				t.Errorf("Expected holiday to be free (0 price), got %d", override.PricePerUnit)
			}
			break
		}
	}
	
	if !foundHoliday {
		t.Error("Expected at least one holiday to be added")
	}
}
*/
