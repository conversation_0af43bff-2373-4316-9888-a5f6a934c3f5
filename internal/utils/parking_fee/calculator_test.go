package parking_fee

import (
	"testing"
	"time"

	"github.com/smooth-inc/backend/internal/domain"
)

func TestCalculateParkingFee(t *testing.T) {
	calculator := NewCalculator()
	
	// Create test configuration similar to overview.md example
	config := domain.PricingRules{
		LotID:              "PARK001",
		InitialFreeMinutes: 15,
		DailyCap:           1500,
		NightCaps: []domain.NightCap{
			{
				Start: "22:00",
				End:   "08:00",
				Cap:   800,
			},
		},
		Overrides: []domain.PriceOverride{
			{
				Name:         "New-Year Holiday",
				Start:        "2025-01-01T00:00",
				End:          "2025-01-03T23:59",
				UnitMinutes:  60,
				PricePerUnit: 0,
			},
		},
		Rules: []domain.PricingRule{
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "15:00",
				End:          "17:00",
				UnitMinutes:  10,
				PricePerUnit: 100,
			},
			{
				Days:         []string{"Mon", "<PERSON>e", "Wed", "Thu", "Fri"},
				Start:        "00:00",
				End:          "15:00",
				UnitMinutes:  30,
				PricePerUnit: 200,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "17:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 150,
			},
			{
				Days:         []string{"Sat", "Sun"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 300,
			},
		},
	}

	tests := []struct {
		name     string
		entry    string
		exit     string
		expected int
	}{
		{
			name:     "Free period only",
			entry:    "2025-05-22T14:00:00+09:00",
			exit:     "2025-05-22T14:10:00+09:00",
			expected: 0,
		},
		{
			name:     "Weekday peak hours",
			entry:    "2025-05-22T15:30:00+09:00", // Thursday 15:30
			exit:     "2025-05-22T16:30:00+09:00", // Thursday 16:30
			expected: 500, // 45 minutes billable (60-15 free), 5 units of 10 min @ 100 each
		},
		{
			name:     "Weekend parking",
			entry:    "2025-05-24T10:00:00+09:00", // Saturday 10:00
			exit:     "2025-05-24T12:00:00+09:00", // Saturday 12:00
			expected: 600, // 105 minutes billable (120-15 free), 2 units of 60 min @ 300 each
		},
		{
			name:     "Holiday override - free",
			entry:    "2025-01-01T10:00:00+09:00",
			exit:     "2025-01-01T15:00:00+09:00",
			expected: 0,
		},
		{
			name:     "Daily cap applied",
			entry:    "2025-05-22T08:00:00+09:00",
			exit:     "2025-05-22T20:00:00+09:00",
			expected: 1500, // Would be more but capped at daily limit
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry, err := time.Parse(time.RFC3339, tt.entry)
			if err != nil {
				t.Fatalf("Failed to parse entry time: %v", err)
			}
			
			exit, err := time.Parse(time.RFC3339, tt.exit)
			if err != nil {
				t.Fatalf("Failed to parse exit time: %v", err)
			}

			result, err := calculator.CalculateParkingFee(entry, exit, config)
			if err != nil {
				t.Fatalf("CalculateParkingFee returned error: %v", err)
			}

			if result != tt.expected {
				t.Errorf("Expected fee %d, got %d", tt.expected, result)
			}
		})
	}
}

func TestNightCapFunctionality(t *testing.T) {
	calculator := NewCalculator()
	
	config := domain.PricingRules{
		LotID:              "PARK001",
		InitialFreeMinutes: 0,
		DailyCap:           0,
		NightCaps: []domain.NightCap{
			{
				Start: "22:00",
				End:   "08:00",
				Cap:   800,
			},
		},
		Rules: []domain.PricingRule{
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 300,
			},
		},
	}

	// Test night cap across midnight
	entry, _ := time.Parse(time.RFC3339, "2025-05-22T23:00:00+09:00")
	exit, _ := time.Parse(time.RFC3339, "2025-05-23T07:00:00+09:00")

	result, err := calculator.CalculateParkingFee(entry, exit, config)
	if err != nil {
		t.Fatalf("CalculateParkingFee returned error: %v", err)
	}

	// Should be capped at 800 even though 8 hours * 300 = 2400
	if result != 800 {
		t.Errorf("Expected night cap of 800, got %d", result)
	}
}

func TestParseTime(t *testing.T) {
	calculator := NewCalculator()
	
	tests := []struct {
		input    string
		expected time.Time
		hasError bool
	}{
		{
			input:    "14:30",
			expected: time.Date(0, 1, 1, 14, 30, 0, 0, time.UTC),
			hasError: false,
		},
		{
			input:    "24:00",
			expected: time.Date(0, 1, 1, 0, 0, 0, 0, time.UTC),
			hasError: false,
		},
		{
			input:    "invalid",
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result, err := calculator.parseTime(tt.input)
			
			if tt.hasError {
				if err == nil {
					t.Errorf("Expected error for input %s, but got none", tt.input)
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error for input %s: %v", tt.input, err)
				return
			}
			
			if !result.Equal(tt.expected) {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestContainsDay(t *testing.T) {
	calculator := NewCalculator()
	
	days := []string{"Mon", "Tue", "Wed", "Thu", "Fri"}
	
	if !calculator.containsDay(days, "Mon") {
		t.Error("Expected Mon to be found in weekdays")
	}
	
	if calculator.containsDay(days, "Sat") {
		t.Error("Expected Sat not to be found in weekdays")
	}
}
