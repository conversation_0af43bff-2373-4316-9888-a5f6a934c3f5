package parking_fee

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

func CreateDefaultConfig(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID) (*domain.ParkingLotConfig, error) {
	return createDefaultConfigWithHolidays(lotID, parkingLotID, createdBy, false)
}

func CreateDefaultConfigWithHolidays(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID, includeHolidays bool) (*domain.ParkingLotConfig, error) {
	return createDefaultConfigWithHolidays(lotID, parkingLotID, createdBy, includeHolidays)
}

func createDefaultConfigWithHolidays(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID, includeHolidays bool) (*domain.ParkingLotConfig, error) {
	pricingRules := domain.PricingRules{
		LotID:              lotID,
		InitialFreeMinutes: 30,
		DailyCap:           2000,
		NightCaps: []domain.NightCap{
			{
				Start: "22:00",
				End:   "08:00",
				Cap:   800,
			},
		},
		Overrides: []domain.PriceOverride{},
		Rules: []domain.PricingRule{
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "08:00",
				End:          "18:00",
				UnitMinutes:  30,
				PricePerUnit: 200,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "18:00",
				End:          "22:00",
				UnitMinutes:  60,
				PricePerUnit: 150,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "22:00",
				End:          "08:00",
				UnitMinutes:  60,
				PricePerUnit: 100,
			},
			{
				Days:         []string{"Sat", "Sun"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 300,
			},
		},
	}

	config, err := domain.NewParkingLotConfig(parkingLotID, "Default Configuration", pricingRules, createdBy)
	if err != nil {
		return nil, err
	}

	if includeHolidays {
		currentYear := time.Now().Year()
		if err := InjectHolidays(config, currentYear); err != nil {
		}
	}

	return config, nil
}

func CreateBusinessDistrictConfig(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID) (*domain.ParkingLotConfig, error) {
	return CreateBusinessDistrictConfigWithHolidays(lotID, parkingLotID, createdBy, false)
}

func CreateBusinessDistrictConfigWithHolidays(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID, includeHolidays bool) (*domain.ParkingLotConfig, error) {
	pricingRules := domain.PricingRules{
		LotID:              lotID,
		InitialFreeMinutes: 15,
		DailyCap:           3000,
		NightCaps: []domain.NightCap{
			{
				Start: "22:00",
				End:   "08:00",
				Cap:   1000,
			},
		},
		Overrides: []domain.PriceOverride{},
		Rules: []domain.PricingRule{
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "09:00",
				End:          "17:00",
				UnitMinutes:  15,
				PricePerUnit: 150,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "07:00",
				End:          "09:00",
				UnitMinutes:  30,
				PricePerUnit: 200,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "17:00",
				End:          "22:00",
				UnitMinutes:  30,
				PricePerUnit: 200,
			},
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "22:00",
				End:          "07:00",
				UnitMinutes:  60,
				PricePerUnit: 150,
			},
			{
				Days:         []string{"Sat", "Sun"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 400,
			},
		},
	}

	config, err := domain.NewParkingLotConfig(parkingLotID, "Business District Configuration", pricingRules, createdBy)
	if err != nil {
		return nil, err
	}

	if includeHolidays {
		currentYear := time.Now().Year()
		if err := InjectHolidays(config, currentYear); err != nil {
		}
	}

	return config, nil
}

func CreateResidentialConfig(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID) (*domain.ParkingLotConfig, error) {
	return CreateResidentialConfigWithHolidays(lotID, parkingLotID, createdBy, false)
}

func CreateResidentialConfigWithHolidays(lotID string, parkingLotID uuid.UUID, createdBy uuid.UUID, includeHolidays bool) (*domain.ParkingLotConfig, error) {
	pricingRules := domain.PricingRules{
		LotID:              lotID,
		InitialFreeMinutes: 60,
		DailyCap:           1500,
		NightCaps: []domain.NightCap{
			{
				Start: "22:00",
				End:   "08:00",
				Cap:   500,
			},
		},
		Overrides: []domain.PriceOverride{},
		Rules: []domain.PricingRule{
			{
				Days:         []string{"Mon", "Tue", "Wed", "Thu", "Fri"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 150,
			},
			{
				Days:         []string{"Sat", "Sun"},
				Start:        "00:00",
				End:          "24:00",
				UnitMinutes:  60,
				PricePerUnit: 200,
			},
		},
	}

	config, err := domain.NewParkingLotConfig(parkingLotID, "Residential Configuration", pricingRules, createdBy)
	if err != nil {
		return nil, err
	}

	if includeHolidays {
		currentYear := time.Now().Year()
		if err := InjectHolidays(config, currentYear); err != nil {
		}
	}

	return config, nil
}

func AddHolidayOverride(config *domain.ParkingLotConfig, name, startDate, endDate string, isFree bool) error {
	var pricePerUnit int
	if isFree {
		pricePerUnit = 0
	} else {
		pricePerUnit = 200
	}

	override := domain.PriceOverride{
		Name:         fmt.Sprintf("Holiday: %s", name),
		Start:        startDate + "T00:00",
		End:          endDate + "T23:59",
		UnitMinutes:  60,
		PricePerUnit: pricePerUnit,
	}

	config.PricingRules.Overrides = append(config.PricingRules.Overrides, override)
	config.UpdatedAt = time.Now()
	return nil
}

func AddSpecialEventOverride(config *domain.ParkingLotConfig, name, startDateTime, endDateTime string, flatRate int) error {
	override := domain.PriceOverride{
		Name:         name,
		Start:        startDateTime,
		End:          endDateTime,
		UnitMinutes:  1,
		PricePerUnit: flatRate,
	}

	config.PricingRules.Overrides = append(config.PricingRules.Overrides, override)
	config.UpdatedAt = time.Now()
	return nil
}

type Holiday struct {
	Date        string   `json:"date"`
	LocalName   string   `json:"localName"`
	Name        string   `json:"name"`
	CountryCode string   `json:"countryCode"`
	Fixed       bool     `json:"fixed"`
	Global      bool     `json:"global"`
	Counties    []string `json:"counties"`
	LaunchYear  int      `json:"launchYear"`
	Types       []string `json:"types"`
}

const HOLIDAY_API = "https://date.nager.at/api/v3/PublicHolidays/%d/JP"

func InjectHolidays(config *domain.ParkingLotConfig, year int) error {
	holidays, err := fetchJapaneseHolidays(year)
	if err != nil {
		return fmt.Errorf("failed to fetch holidays for year %d: %w", year, err)
	}

	for _, holiday := range holidays {
		holidayDate, err := time.Parse("2006-01-02", holiday.Date)
		if err != nil {
			continue // Skip invalid dates
		}

		override := domain.PriceOverride{
			Name:         fmt.Sprintf("Holiday: %s", holiday.LocalName),
			Start:        holidayDate.Format("2006-01-02T00:00"),
			End:          holidayDate.Format("2006-01-02T23:59"),
			UnitMinutes:  60,
			PricePerUnit: 0,
		}

		config.PricingRules.Overrides = append(config.PricingRules.Overrides, override)
	}

	config.UpdatedAt = time.Now()
	return nil
}

func fetchJapaneseHolidays(year int) ([]Holiday, error) {
	url := fmt.Sprintf(HOLIDAY_API, year)

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch holidays from API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("holiday API returned status %d", resp.StatusCode)
	}

	var holidays []Holiday
	if err := json.NewDecoder(resp.Body).Decode(&holidays); err != nil {
		return nil, fmt.Errorf("failed to decode holiday response: %w", err)
	}

	return holidays, nil
}

func InjectHolidaysForYears(config *domain.ParkingLotConfig, years []int) error {
	for _, year := range years {
		if err := InjectHolidays(config, year); err != nil {
			return fmt.Errorf("failed to inject holidays for year %d: %w", year, err)
		}
	}
	return nil
}

func RemoveExpiredHolidays(config *domain.ParkingLotConfig, cutoffDate time.Time) {
	var validOverrides []domain.PriceOverride

	for _, override := range config.PricingRules.Overrides {
		if len(override.Name) > 8 && override.Name[:8] == "Holiday:" {
			endTime, err := time.Parse("2006-01-02T15:04", override.End)
			if err != nil {
				validOverrides = append(validOverrides, override)
				continue
			}

			if endTime.After(cutoffDate) {
				validOverrides = append(validOverrides, override)
			}
		} else {
			validOverrides = append(validOverrides, override)
		}
	}

	config.PricingRules.Overrides = validOverrides
	config.UpdatedAt = time.Now()
}
