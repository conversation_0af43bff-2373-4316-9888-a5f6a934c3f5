package parking_fee

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// ExampleUsage demonstrates how to use the parking fee calculator with holiday integration
func ExampleUsage() {
	// Create a parking lot configuration with holidays
	lotID := "SHIBUYA_001"
	parkingLotID := uuid.New()
	createdBy := uuid.New()

	// Create a default configuration with current year holidays
	config, err := CreateDefaultConfigWithHolidays(lotID, parkingLotID, createdBy, true)
	if err != nil {
		fmt.Printf("Error creating config: %v\n", err)
		return
	}

	fmt.Printf("Created parking lot configuration for %s\n", lotID)
	fmt.Printf("Initial free minutes: %d\n", config.PricingRules.InitialFreeMinutes)
	fmt.Printf("Daily cap: ¥%d\n", config.PricingRules.DailyCap)
	fmt.Printf("Number of pricing rules: %d\n", len(config.PricingRules.Rules))
	fmt.Printf("Number of overrides (including holidays): %d\n", len(config.PricingRules.Overrides))

	// Add a special event override
	err = AddSpecialEventOverride(config, "Summer Festival", "2025-07-15T18:00", "2025-07-15T23:00", 1500)
	if err != nil {
		fmt.Printf("Error adding special event: %v\n", err)
		return
	}

	// Add a custom holiday
	err = AddHolidayOverride(config, "Company Holiday", "2025-08-15", "2025-08-15", true)
	if err != nil {
		fmt.Printf("Error adding custom holiday: %v\n", err)
		return
	}

	// Calculate parking fee for different scenarios
	calculator := NewCalculator()

	// Scenario 1: Regular weekday parking
	entry1, _ := time.Parse(time.RFC3339, "2025-06-17T09:00:00+09:00")
	exit1, _ := time.Parse(time.RFC3339, "2025-06-17T17:00:00+09:00")
	fee1, err := calculator.CalculateParkingFee(entry1, exit1, config.PricingRules)
	if err != nil {
		fmt.Printf("Error calculating fee 1: %v\n", err)
	} else {
		fmt.Printf("Weekday 8-hour parking fee: ¥%d\n", fee1)
	}

	// Scenario 2: Weekend parking
	entry2, _ := time.Parse(time.RFC3339, "2025-06-21T10:00:00+09:00")
	exit2, _ := time.Parse(time.RFC3339, "2025-06-21T15:00:00+09:00")
	fee2, err := calculator.CalculateParkingFee(entry2, exit2, config.PricingRules)
	if err != nil {
		fmt.Printf("Error calculating fee 2: %v\n", err)
	} else {
		fmt.Printf("Weekend 5-hour parking fee: ¥%d\n", fee2)
	}

	// Scenario 3: Holiday parking (should be free if it's a Japanese holiday)
	entry3, _ := time.Parse(time.RFC3339, "2025-01-01T10:00:00+09:00") // New Year's Day
	exit3, _ := time.Parse(time.RFC3339, "2025-01-01T15:00:00+09:00")
	fee3, err := calculator.CalculateParkingFee(entry3, exit3, config.PricingRules)
	if err != nil {
		fmt.Printf("Error calculating fee 3: %v\n", err)
	} else {
		fmt.Printf("Holiday parking fee: ¥%d\n", fee3)
	}

	// Scenario 4: Night parking with night cap
	entry4, _ := time.Parse(time.RFC3339, "2025-06-17T22:00:00+09:00")
	exit4, _ := time.Parse(time.RFC3339, "2025-06-18T07:00:00+09:00")
	fee4, err := calculator.CalculateParkingFee(entry4, exit4, config.PricingRules)
	if err != nil {
		fmt.Printf("Error calculating fee 4: %v\n", err)
	} else {
		fmt.Printf("Overnight parking fee (with night cap): ¥%d\n", fee4)
	}

	// Demonstrate holiday management
	fmt.Println("\nHoliday Management:")
	
	// Inject holidays for next year
	nextYear := time.Now().Year() + 1
	err = InjectHolidays(config, nextYear)
	if err != nil {
		fmt.Printf("Error injecting holidays for %d: %v\n", nextYear, err)
	} else {
		fmt.Printf("Successfully injected holidays for %d\n", nextYear)
	}

	// Remove expired holidays (older than 1 year ago)
	cutoffDate := time.Now().AddDate(-1, 0, 0)
	RemoveExpiredHolidays(config, cutoffDate)
	fmt.Printf("Removed expired holidays older than %s\n", cutoffDate.Format("2006-01-02"))
	fmt.Printf("Current number of overrides: %d\n", len(config.PricingRules.Overrides))

	// Show some of the overrides
	fmt.Println("\nCurrent overrides:")
	for i, override := range config.PricingRules.Overrides {
		if i >= 5 { // Show only first 5
			fmt.Printf("... and %d more\n", len(config.PricingRules.Overrides)-5)
			break
		}
		fmt.Printf("- %s: %s to %s (¥%d)\n", 
			override.Name, 
			override.Start, 
			override.End, 
			override.PricePerUnit)
	}
}

// ExampleBusinessDistrictUsage shows how to create and use a business district configuration
func ExampleBusinessDistrictUsage() {
	lotID := "MARUNOUCHI_001"
	parkingLotID := uuid.New()
	createdBy := uuid.New()

	// Create business district configuration with holidays
	config, err := CreateBusinessDistrictConfigWithHolidays(lotID, parkingLotID, createdBy, true)
	if err != nil {
		fmt.Printf("Error creating business district config: %v\n", err)
		return
	}

	fmt.Printf("Created business district configuration for %s\n", lotID)
	fmt.Printf("Free minutes: %d (shorter for business district)\n", config.PricingRules.InitialFreeMinutes)
	fmt.Printf("Daily cap: ¥%d (higher for business district)\n", config.PricingRules.DailyCap)

	calculator := NewCalculator()

	// Peak business hours parking
	entry, _ := time.Parse(time.RFC3339, "2025-06-17T09:30:00+09:00")
	exit, _ := time.Parse(time.RFC3339, "2025-06-17T11:30:00+09:00")
	fee, err := calculator.CalculateParkingFee(entry, exit, config.PricingRules)
	if err != nil {
		fmt.Printf("Error calculating business district fee: %v\n", err)
	} else {
		fmt.Printf("Business district 2-hour peak parking fee: ¥%d\n", fee)
	}
}
