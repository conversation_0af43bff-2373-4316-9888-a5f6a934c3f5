Architecture
# Overview

This product adopts a DDD-like layered architecture

If you are not familiar with architecture, DDD, or layered architecture, see the following docs

- https://zenn.dev/nameless_sn/articles/what_is_software_architechture
- https://qiita.com/ROPITAL/items/165bef33492ba27cfbf7
    - It may be difficult, so just get a feel for it.
- https://mintaku-blog.net/go-ddd/
    - It's OK if you read up to “各レイヤの責務と役割” at least
- https://zenn.dev/kohii/articles/b96634b9a14897
    - It may be difficult, so it's OK if you can get a feel for it.

# Dependencies and calling relationships

## Conceptual level

- The following dependencies and invocation relationships are employed
- The usecase calls the repository so that the domain doesn’t depend on the repository

sql
controller / batch -> usecase -> domain / repository / gateway


### controller

- This is used to receive HTTP requests, invoke usecase, and return HTTP responses, and the specific logic is described after usecase.

### usecase

- Basically, it is dedicated to domain / repository calls, and only bridges domain and repository.

### domain

- Describe the domain logic. DON’T describe domain logic in other layers.
- ref; https://little-hands.hatenablog.com/entry/2019/07/26/domain-knowledge

### repository

- Describe DB operations

## Implementation level

e.g. When POST /users is called, ..

1. apigen/server.gen.go
    - RegisterHandlersWithBaseURL -> wrapper.PostUsers -> w.Handler.PostUsers(ctx)
2. controller/user.go
    - controller.PostUsers
3. usecase/user.go
    - UserUsecase.Create
4. domain/user/user.go
    - NewToSave
5. repository/user.go
    - UserRepository.Create

### Supplementation

- The controller struct in controller/controller.go implements the ServerInterface interface in apigen/server.gen.go.
- Therefore, calling w.Handler.PostUsers(ctx) will call controller.PostUsers.
- ref; https://qiita.com/rtok/items/46eadbf7b0b7a1b0eb08